# Пример файла переменных окружения для Telegram-бота погоды
# Скопируйте этот файл в .env и заполните своими значениями

# =============================================================================
# ОБЯЗАТЕЛЬНЫЕ ПЕРЕМЕННЫЕ
# =============================================================================

# Токен вашего Telegram-бота (получить у @BotFather)
TELEGRAM_TOKEN=your_telegram_bot_token_here

# ID канала, где будет работать бот (например: @your_channel или -1001234567890)
CHANNEL_ID=your_channel_id_here

# =============================================================================
# ОПЦИОНАЛЬНЫЕ ПЕРЕМЕННЫЕ (есть значения по умолчанию)
# =============================================================================

# WeatherAPI.com ключ (по умолчанию используется ключ из плана разработки)
# WEATHERAPI_KEY=your_weatherapi_key_here

# Cerebras API ключ (по умолчанию используется ключ из плана разработки)
# CEREBRAS_API_KEY=your_cerebras_api_key_here

# =============================================================================
# ИНСТРУКЦИИ ПО ПОЛУЧЕНИЮ КЛЮЧЕЙ
# =============================================================================

# 1. Telegram Bot Token:
#    - Напишите @BotFather в Telegram
#    - Создайте нового бота командой /newbot
#    - Следуйте инструкциям и получите токен

# 2. Channel ID:
#    - Создайте канал в Telegram
#    - Добавьте бота как администратора с правами на публикацию
#    - Для получения ID канала можете использовать @userinfobot

# 3. WeatherAPI.com ключ (опционально):
#    - Зарегистрируйтесь на https://www.weatherapi.com/
#    - Получите бесплатный API ключ

# 4. Cerebras API ключ (опционально):
#    - Зарегистрируйтесь на https://cerebras.ai/
#    - Получите API ключ для модели qwen-3-235b-a22b-instruct-2507
