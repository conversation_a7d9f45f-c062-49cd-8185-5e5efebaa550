# Настройка конфигурации Weather Bot

## Обзор

Этот документ описывает настройку конфигурации для Telegram-бота погоды после выполнения **Этапа 2** плана разработки.

## Что было выполнено

✅ **Создан файл `weather_config.py`** со всеми необходимыми константами:
- API токены и ключи (с поддержкой переменных окружения)
- Географические координаты Гагаринского района Севастополя
- Временные настройки и интервалы обновления
- URL для WeatherAPI.com API
- Настройки Cerebras API
- Словарь из 45 изображений для разных погодных условий
- Функции проверки конфигурации

✅ **Создан файл `.env.example`** с примером настройки переменных окружения

✅ **Проверено существование всех 45 файлов изображений** в папке `images/`

## Структура конфигурации

### API ключи и токены
```python
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN', '')
WEATHERAPI_KEY = os.getenv('WEATHERAPI_KEY', 'ecdd38215c144d2588b142720253107')
CEREBRAS_API_KEY = os.getenv('CEREBRAS_API_KEY', 'csk-cnjd9w9nx5kjtpj99936fexmhpxdc586ny3k6rc96rfn4rkw')
```

### Географические координаты
```python
LAT = 44.600  # Гагаринский район Севастополя
LON = 33.517
TZ = 'Europe/Moscow'  # Московское время
```

### Интервалы обновления
```python
UPDATE_INTERVAL_WEEK = 43200      # 12 часов для прогноза на 3 дня
UPDATE_INTERVAL_TODAY = 3600      # 1 час для прогноза на сегодня  
UPDATE_INTERVAL_CURRENT = 600     # 10 минут для текущей погоды
```

### Изображения погоды
45 изображений для различных погодных условий и времени суток:
- Ясная погода (день/вечер/ночь)
- Облачность разной степени
- Осадки (дождь, снег, град, ледяной дождь)
- Особые условия (туман, дымка, ветер, метель, мороз)
- Грозы

## Следующие шаги

Для продолжения разработки необходимо:

1. **Настроить переменные окружения:**
   ```bash
   cp .env.example .env
   # Отредактировать .env файл с вашими токенами
   ```

2. **Получить необходимые токены:**
   - Telegram Bot Token от @BotFather
   - ID канала для публикации прогнозов
   - (Опционально) Собственные ключи WeatherAPI.com и Cerebras

3. **Проверить конфигурацию:**
   ```bash
   python weather_config.py
   ```

4. **Перейти к Этапу 3:** Реализация модуля работы с WeatherAPI.com

## Функции проверки

Модуль включает функции для проверки корректности настроек:

- `validate_config()` - проверяет все настройки и файлы
- `get_image_path()` - безопасно получает путь к изображению

## Безопасность

- Все чувствительные данные читаются из переменных окружения
- Предоставлены fallback значения для API ключей из плана разработки
- Файл `.env` должен быть добавлен в `.gitignore`

---

**Статус:** ✅ Этап 2 завершен  
**Следующий этап:** Этап 3 - Реализация модуля работы с WeatherAPI.com
